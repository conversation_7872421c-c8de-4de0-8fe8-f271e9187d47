import net from 'net';
import { v4 as uuidv4 } from 'uuid';

// Get command line arguments (skip first two arguments as they are node and script path)
const args = process.argv.slice(2);
console.log('Command line arguments:', args);

const client = new net.Socket();
// const dirPath = '/Users/<USER>/Documents/work/kwaipilot-fe';
// const dirPath = '/Users/<USER>/Documents/work/ide-agent';
// const dirPath = '/Users/<USER>/Documents/work/agents-engine';
// const dirPath = '/Users/<USER>/Documents/work/android-client';
const dirPath = '/Users/<USER>/Documents/work/doraemon';
// const dirPath = '/Users/<USER>/Documents/work/中文/web-reader-for-llm';
const PORT = process.env.PORT || 30001;
const git_url = '*************************:gif/android-client.git';
client.connect(PORT, '127.0.0.1', async () => {
  console.log('Connecting to server');
});
client.on('connect', () => {
  console.log('Connected to server');
  const commonInfo = {
    pluginVersion: '1.0.26', // 插件版本
    version: '1.0.26', // IDE 版本
    platform: 'vscode', // IDE 平台
    repo: {
      git_url, // 完整git地址
      dir_path: dirPath, // 目录地址
      commit: '8745b7feea0e' // git commit id
    }
  };

  // 发送消息示例
  const message = {
    common: commonInfo,
    messageType: 'state/agentState',
    data: {
      msg: 'ping'
    },
    messageId: uuidv4()
  };

  client.write(JSON.stringify(message) + '\r\n');
  if (args.length > 0) {
    console.log('First argument:', args[0]);
    if (args[0] === 'index') {
      indexRepo(message);
    }
    if (args[0] === 'clear') {
      clearIndex(message);
    }
    if (args[0] === 'search') {
      makeSearch(message);
    }
    if (args[0] === 'file') {
      const file = '/Users/<USER>/Documents/work/ide-agent/test-repo/napi_init.cpp';
      indexFiles(message, file);
    }
    if (args[0] === 'indexedFileList') {
      indexedFileList(message);
    }
    if (args[0] === 'rules') {
      getRulesList(message);
    }
    if (args[0] === 'projectInfo') {
      getProjectInfo(message);
    }
  }
});
const getProjectInfo = async (message) => {
  message.messageType = 'project/getProjectWiki';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};
const getRulesList = async (message) => {
  message.messageType = 'rules/getRulesList';
  message.messageId = uuidv4();
  message.data = {
    rules: ['.kwaipilot/rules/a.md', '.kwaipilot/rules/d.md']
  };
  client.write(JSON.stringify(message) + '\r\n');
};
const indexedFileList = async (message) => {
  message.messageType = 'index/indexedFileList';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};
const indexRepo = async (message) => {
  try {
    message.messageType = 'index/repoIndex';
    message.messageId = uuidv4();
    client.write(JSON.stringify(message) + '\r\n');
  } catch (error) {
    console.error('Error while indexing files:', error);
  }
};
const indexFiles = async (message, filePath) => {
  message.messageType = 'index/file';
  message.messageId = uuidv4();
  const options = {
    file: filePath,
    action: 'create'
  };
  message.data = options;
  client.write(JSON.stringify(message) + '\r\n');
};

const makeSearch = (message) => {
  return new Promise((resolve, reject) => {
    message.messageType = 'search/search';
    message.messageId = uuidv4();
    const options = {
      query: '#{repo} markdwon\n\n',
      topK: 10,
      chatHistory: [],
      targetDirectory: ['packages/server/src/middlewares']
    };
    message.data = options;
    client.write(JSON.stringify(message) + '\r\n');
  });
};

const makeSearchByPath = (message) => {
  message.messageType = 'search/searchByPath';
  message.messageId = uuidv4();
  const options = {
    query: '.',
    topK: 100,
    vercel: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    targetDirectory: ['apps/core/code/analyze']
  };
  message.data = options;
  client.write(JSON.stringify(message) + '\r\n');
};

const buildIndex = (message) => {
  message.messageType = 'index/repoIndex';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};

const pauseIndex = (message) => {
  message.messageType = 'index/pause';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};

const startIndex = (message) => {
  message.messageType = 'index/build';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};

const clearIndex = (message) => {
  message.messageType = 'index/clearIndex';
  message.messageId = uuidv4();
  const options = {};
  message.data = options;
  client.write(JSON.stringify(message) + '\r\n');
};

async function handleMessage(data) {
  console.log('Received:', data);
  try {
    const arr = data.split('\r\n');
    for (const item of arr) {
      if (item) {
        const message = JSON.parse(item);
        console.log(message);
        if (message.messageId && message.messageType === 'config/getIdeSetting') {
          message.data = {
            data: {
              dirPath: dirPath
            }
          };
          console.log(message);
          client.write(JSON.stringify(message) + '\r\n');
        }
        if (message.messageId && message.messageType === 'state/ideState') {
          message.status = 'ok';
          console.log(message);
          client.write(JSON.stringify(message) + '\r\n');
        }
        if (message.messageId && message.messageType === 'state/ideInfo') {
          if (!message.data || !message.data.data) {
            message.data = {
              data: {
                cwd: dirPath,
                maxIndexSpace: 1,
                repoInfo: {
                  dir_path: dirPath,
                  git_url,
                  commit: 'undefined'
                },
                userInfo: {
                  name: 'renbeihai'
                }
              }
            };
          }
          message.data.data.proxyUrl = 'https://kwaipilot.corp.kuaishou.com/';
          message.data.data.ideVersion = '1.0.26';
          message.data.data.pluginVersion = '8.2.1';
          message.data.data.platform = 'vscode';

          console.log(message);
          client.write(JSON.stringify(message) + '\r\n');
        }
      }
    }
    return 'pong';
  } catch (e) {
    console.log(e);
  }
}

client.on('data', (data) => {
  console.log('Received:', data.toString());
  handleMessage(data.toString());
});

client.on('close', () => {
  console.log('Connection closed');
});

client.on('error', (err) => {
  console.error('Connection error:', err);
});
