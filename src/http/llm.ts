export const AI_GATEWAY_URL = 'https://kwaipilot.corp.kuaishou.com/nodeapi/indexing/project-info/chat';

export const LLM_HEADERS = {
  'Content-Type': 'application/json'
};

// 重试配置
export const RETRY_CONFIG = {
  enabled: true, // 是否启用重试机制
  maxRetries: 3,
  baseDelay: 1000, // 基础延迟时间（毫秒）
  maxDelay: 10000, // 最大延迟时间（毫秒）
  backoffMultiplier: 2 // 指数退避倍数
};

// 判断是否为可重试的错误
function isRetryableError(error: any): boolean {
  // 网络错误
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
    return true;
  }

  // HTTP状态码错误
  if (error.status) {
    const status = error.status;
    // 5xx 服务器错误和某些 4xx 错误可以重试
    return status >= 500 || status === 408 || status === 429;
  }

  // fetch 错误
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return true;
  }

  return false;
}

// 计算延迟时间（指数退避）
function calculateDelay(attempt: number): number {
  const delay = RETRY_CONFIG.baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt);
  return Math.min(delay, RETRY_CONFIG.maxDelay);
}

// 延迟执行
function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export async function callLLM(messages: any[], tools: any[] = []): Promise<any> {
  const requestBody: any = {
    messages: messages,
    temperature: 0
  };

  // 如果有tools参数，则添加到请求体中
  if (tools && tools.length > 0) {
    requestBody.tools = tools;
  }

  let lastError: Error | undefined;
  const maxRetries = RETRY_CONFIG.enabled ? RETRY_CONFIG.maxRetries : 0;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      console.log(`LLM调用尝试 ${attempt + 1}/${maxRetries + 1}`);

      const response = await fetch(AI_GATEWAY_URL, {
        method: 'POST',
        headers: LLM_HEADERS,
        body: JSON.stringify(requestBody)
      });

      // 检查HTTP状态码
      if (!response.ok) {
        const errorInfo = {
          status: response.status,
          statusText: response.statusText,
          url: response.url
        };

        const error = new Error(`HTTP ${response.status}: ${response.statusText}`) as any;
        error.status = response.status;
        error.response = response;

        // 如果是最后一次尝试或错误不可重试，直接抛出
        if (attempt === maxRetries || !isRetryableError(error)) {
          console.error('LLM调用失败 (HTTP错误):', errorInfo);
          throw error;
        }

        console.warn(`LLM调用失败 (HTTP错误), 将在 ${calculateDelay(attempt)}ms 后重试:`, errorInfo);
        lastError = error;
      } else {
        // 成功响应
        const result = await response.json();
        if (result.code === 0) {
          return result;
        }
        if (attempt > 0) {
          console.log(`LLM调用在第 ${attempt + 1} 次尝试后成功`);
        }

        return result;
      }
    } catch (error: any) {
      const errorInfo = {
        message: error.message,
        code: error.code,
        name: error.name,
        attempt: attempt + 1
      };

      // 如果是最后一次尝试或错误不可重试，直接抛出
      if (attempt === maxRetries || !isRetryableError(error)) {
        console.error('LLM调用最终失败:', errorInfo);
        throw error;
      }

      const delay = calculateDelay(attempt);
      console.warn(`LLM调用失败, 将在 ${delay}ms 后重试 (${attempt + 1}/${maxRetries}):`, errorInfo);

      lastError = error;
    }

    // 如果不是最后一次尝试，等待后重试
    if (attempt < maxRetries) {
      const delay = calculateDelay(attempt);
      await sleep(delay);
    }
  }

  // 如果所有重试都失败了，抛出最后一个错误
  throw lastError || new Error('LLM调用失败：达到最大重试次数');
}
