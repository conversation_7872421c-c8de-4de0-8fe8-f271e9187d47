import mime from 'mime-types';
import path from 'path';
import { readdir, readFile, stat } from 'fs/promises';
import { Logger } from '@/util/log';

const logger: Logger = new Logger('IndexManager-Utils');

// 使用 ignore 库创建忽略规则
export const IG_PATERNS = [
  '**/node_modules/**',
  'node_modules/**',
  '.git/**',
  '.idea/**',
  '.vscode/**',
  '.DS_Store',
  '.env*',
  'dist/**',
  'build/**',
  '*.gitignore',
  '*..gitignore~*',
  '*.log',
  '*.Processor',
  '*.directory',
  '*.otf',
  '*.webp',
  '*.jar',
  '*.aac',
  '*.pb',
  '*.puml',
  '*.lock',
  'package-lock.json',
  'yarn.lock',
  '*.map',
  '*.min.js',
  '*.min.css',
  // 各种媒体文件和二进制文件
  '*.jpg',
  '*.jpeg',
  '*.png',
  '*.gif',
  '*.ico',
  '*.svg',
  '*.ogg',
  '*.keystore',
  '*.cfg',
  '*.m4a',
  '*.woff',
  '*.woff2',
  '*.ttf',
  '*.eot',
  '*.mp3',
  '*.mp4',
  '*.avi',
  '*.mkv',
  '*.zip',
  '*.tar',
  '*.gz',
  '*.rar',
  '*.7z',
  '*.exe',
  '*.dll',
  '*.so',
  '*.dylib',
  '*.vsix',
  '*.class',
  // 编译后的文件
  '*.d.ts',
  '*.d.mts',
  '*.d.cts',
  '*.js.flow',
  // 各种模块格式的编译输出
  '*.es.js',
  '*.es.ts',
  '*.es.jsx',
  '*.es.tsx',
  '*.umd.js',
  '*.umd.ts',
  '*.umd.jsx',
  '*.umd.tsx',
  '*.amd.js',
  '*.amd.ts',
  '*.amd.jsx',
  '*.amd.tsx',
  '*.cjs.js',
  '*.cjs.ts',
  '*.cjs.jsx',
  '*.cjs.tsx',
  '*.esm.js',
  '*.esm.ts',
  '*.esm.jsx',
  '*.esm.tsx',
  '*.iife.js',
  '*.iife.ts',
  '*.iife.jsx',
  '*.iife.tsx',
  '*.system.js',
  '*.system.ts',
  '*.system.jsx',
  '*.system.tsx',
  '*.commonjs.js',
  '*.commonjs.ts',
  '*.commonjs.jsx',
  '*.commonjs.tsx',
  // 环境特定的编译输出
  '*.prod.js',
  '*.prod.ts',
  '*.prod.jsx',
  '*.prod.tsx',
  '*.dev.js',
  '*.dev.ts',
  '*.dev.jsx',
  '*.dev.tsx',
  '*.production.js',
  '*.production.ts',
  '*.production.jsx',
  '*.production.tsx',
  '*.development.js',
  '*.development.ts',
  '*.development.jsx',
  '*.development.tsx',
  '*.test.js',
  '*.test.ts',
  '*.test.jsx',
  '*.test.tsx',
  '*.testing.js',
  '*.testing.ts',
  '*.testing.jsx',
  '*.testing.tsx',
  // 打包或压缩后的文件
  '*.bundle.js',
  '*.bundle.ts',
  '*.bundle.jsx',
  '*.bundle.tsx',
  '*.bundled.js',
  '*.bundled.ts',
  '*.bundled.jsx',
  '*.bundled.tsx',
  '*.compiled.js',
  '*.compiled.ts',
  '*.compiled.jsx',
  '*.compiled.tsx',
  '*.min.js',
  '*.min.ts',
  '*.min.jsx',
  '*.min.tsx',
  '*.minified.js',
  '*.minified.ts',
  '*.minified.jsx',
  '*.minified.tsx',
  // 目标环境特定的编译输出
  '*.browser.js',
  '*.browser.ts',
  '*.browser.jsx',
  '*.browser.tsx',
  '*.node.js',
  '*.node.ts',
  '*.node.jsx',
  '*.node.tsx',
  '*.modern.js',
  '*.modern.ts',
  '*.modern.jsx',
  '*.modern.tsx',
  '*.legacy.js',
  '*.legacy.ts',
  '*.legacy.jsx',
  '*.legacy.tsx',
  '*.compat.js',
  '*.compat.ts',
  '*.compat.jsx',
  '*.compat.tsx'
];

// 添加已知的文本文件扩展名集合
export const TEXT_FILE_EXTENSIONS = new Set([
  // 配置文件
  '.mts',
  '.cts',
  '.cjs',
  '.mjs',
  '.pro',
  '.json',
  '.xml',
  '.yaml',
  '.yml',
  '.toml',
  '.ini',
  '.conf',
  '.config',
  '.properties',
  '.prop',
  '.env',
  '.gradle',
  '.pom',
  '.maven',
  '.dockerfile',
  '.dockerignore',
  '.gitignore',
  '.npmrc',
  '.nvmrc',
  '.eslintrc',
  '.prettierrc',
  '.babelrc',
  '.tsconfig',
  '.editorconfig',

  // 编程语言
  '.ts',
  '.js',
  '.jsx',
  '.tsx',
  '.py',
  '.rb',
  '.php',
  '.java',
  '.c',
  '.cpp',
  '.h',
  '.hpp',
  '.cs',
  '.go',
  '.rs',
  '.swift',
  '.kt',
  '.scala',
  '.r',
  '.m',
  '.lua',
  '.pl',
  '.sh',
  '.bash',
  '.ps1',
  '.psm1',

  // Web 相关
  '.html',
  '.htm',
  '.css',
  '.scss',
  '.sass',
  '.less',
  '.vue',
  '.svelte',
  '.wxml',
  '.wxss',

  // 标记语言
  '.md',
  '.markdown',
  '.rst',
  '.tex',
  '.latex',
  '.wiki',

  // 数据文件
  '.sql',
  '.graphql',
  '.gql',
  '.csv',
  '.tsv',

  // 其他文本文件
  '.txt',
  '.log'
]);

/**
 * 检查文件是否为文本文件
 * @param filePath 文件路径
 * @returns boolean
 */
export const isTextFile = async (filePath: string, dirPath: string): Promise<boolean> => {
  try {
    // 检查文件扩展名
    const ext = path.extname(filePath).toLowerCase();

    // 特别检查SVG文件
    if (ext === '.svg') {
      logger.debug(`File ${filePath} is a SVG file, skipping`);
      return false;
    }

    // 如果在已知的文本文件扩展名列表中，直接返回 true
    if (TEXT_FILE_EXTENSIONS.has(ext)) {
      return true;
    }

    // 获取MIME类型
    const mimeType = mime.lookup(filePath);
    logger.debug(`File ${filePath} mime type: ${mimeType}`);

    // 如果是SVG mime类型，返回false
    if (mimeType === 'image/svg+xml') {
      logger.debug(`File ${filePath} is SVG by mime type, skipping`);
      return false;
    }

    // 如果没有扩展名，尝试读取文件的前几个字节来判断是否是二进制文件
    if (!ext) {
      try {
        const fullPath = path.join(dirPath, filePath);
        const buffer = await readFile(fullPath, { encoding: null, flag: 'r' });

        // 检查前 1024 个字节中是否包含空字节（二进制文件的特征）
        for (let i = 0; i < Math.min(buffer.length, 1024); i++) {
          if (buffer[i] === 0) {
            logger.debug(`File ${filePath} appears to be binary (contains null bytes)`);
            return false;
          }
        }
        return true;
      } catch (error) {
        logger.warn(`Error reading file ${filePath} for binary check:`, error);
        return false;
      }
    }

    // 如果扩展名不在列表中，使用 mime 类型判断
    if (!mimeType) {
      logger.debug(`No mime type found for ${filePath}, treating as non-text file`);
      return false;
    }
    // 检查 mime 类型
    const isText =
      mimeType.startsWith('text/') ||
      mimeType.includes('javascript') ||
      mimeType.includes('json') ||
      mimeType.includes('xml') ||
      mimeType.includes('yaml') ||
      mimeType.includes('typescript');

    if (!isText) {
      logger.debug(`File ${filePath} has non-text mime type: ${mimeType}`);
    }

    return isText;
  } catch (error) {
    logger.error(`Error in isTextFile for ${filePath}:`, error);
    return false;
  }
};

export async function generateDirectoryStructureText(projectPath: string, indent: string = '    '): Promise<string> {
  let structureText = `${path.basename(projectPath)}/\n`;

  try {
    // 获取根目录下的文件和目录
    const entries = await readdir(projectPath);

    // 遍历根目录下的文件和目录
    for (const entry of entries) {
      // 跳过隐藏文件和目录
      if (entry.startsWith('.')) {
        continue;
      }

      const entryPath = path.join(projectPath, entry);
      const stats = await stat(entryPath);

      if (stats.isDirectory()) {
        // 如果是目录,添加目录名称到目录结构文本中
        structureText += `${indent}├── ${entry}/\n`;

        // 如果是 src 目录,则再展开一层
        if (entry === 'src') {
          try {
            const subEntries = await readdir(entryPath);

            for (const subEntry of subEntries) {
              // 跳过隐藏文件和目录
              if (subEntry.startsWith('.')) {
                continue;
              }

              const subEntryPath = path.join(entryPath, subEntry);
              const subStats = await stat(subEntryPath);

              if (subStats.isDirectory()) {
                // 如果是目录,添加目录名称到目录结构文本中
                structureText += `${indent.repeat(2)}├── ${subEntry}/\n`;
              } else {
                // 如果是文件,添加文件名称到目录结构文本中
                structureText += `${indent.repeat(2)}├── ${subEntry}\n`;
              }
            }
          } catch (error) {
            logger.warn(`Error reading src directory ${entryPath}:`, error);
          }
        }
      } else {
        // 如果是文件,添加文件名称到目录结构文本中
        structureText += `${indent}├── ${entry}\n`;
      }
    }
  } catch (error) {
    logger.error(`Error reading directory ${projectPath}:`, error);
    structureText += `${indent}Error reading directory\n`;
  }

  return structureText;
}
