export interface FileNode {
  name: string;
  type: 'file' | 'directory';
  children?: FileNode[];
  depth: number;
}

export interface ToolCall {
  id: string;
  name: string;
  input: any;
}

export interface SystemMessage {
  role: 'system';
  content: string;
}

export interface UserMessage {
  role: 'user';
  content: string;
}

export interface AssistantMessage {
  role: 'assistant';
  content?: string;
  tool_calls?: ToolCall[];
}

export interface SupervisorMessage {
  role: 'supervisor';
  content: string;
  validation_result: {
    is_complete: boolean;
    needs_improvement: string[];
    additional_evidence_needed: string[];
  };
}

export interface ToolMessage {
  role: 'tool';
  tool_call_id: string;
  content: string;
}

export type ChatMessage = SystemMessage | UserMessage | AssistantMessage | ToolMessage | SupervisorMessage;

export interface ProjectInfoDetail {
  architecture: string;
  development_guide: string;
  project_overview: string;
  tech_stack_and_preferences: string;
  path: string;
  lastUpdated: string;
}
