// Schema definition for LLM function calling
export const getProjectStructureSchema = {
  type: 'function',
  function: {
    name: 'getProjectStructure',
    description: 'Recursively reads and returns the directory structure of a specified path up to 3 levels deep',
    parameters: {
      type: 'object',
      required: ['dirPath'],
      properties: {
        dirPath: {
          type: 'string',
          description: 'The absolute path of the directory to analyze'
        }
      }
    }
  }
};

export const getFileContentSchema = {
  type: 'function',
  function: {
    name: 'getFileContent',
    description: 'Reads and returns the content of a specified file',
    parameters: {
      type: 'object',
      required: ['filePath'],
      properties: {
        filePath: {
          type: 'string',
          description: 'The absolute path of the file to read'
        }
      }
    }
  }
};

export const searchCodeBaseSchema = {
  type: 'function',
  function: {
    name: 'searchCodeBase',
    description: 'Performs a semantic search in the codebase using embeddings and returns relevant code snippets',
    parameters: {
      type: 'object',
      required: ['query'],
      properties: {
        query: {
          type: 'string',
          description: 'The search query to find relevant code snippets'
        },
        targetDirectory: {
          type: 'array',
          description: 'The relative path of the directory to search in, do not include the project root path',
          items: {
            type: 'string'
          }
        }
      }
    }
  }
};

export const writeFileSchema = {
  type: 'function',
  function: {
    name: 'writeFile',
    description: 'Writes a string to a file',
    parameters: {
      type: 'object',
      required: ['filePath', 'content'],
      properties: {
        filePath: {
          type: 'string',
          description: 'The relative path of the file to write, do not include the project root path'
        },
        content: {
          type: 'string',
          description: 'The content to write to the file'
        }
      }
    }
  }
};
