import { Api } from '@/http';
import { SearchManager } from '@/indexing/SearchManager';
import path from 'path';
import { FileNode } from './types';
import fs from 'fs';
import { GlobalConfig } from '@/util/global';
import { getKwaipilotGlobalPath, md5 } from '@/util/paths';
import { Logger } from '@/util/log';
const logger = new Logger('project-wiki-functions');

export function getFileContent(filePath: string) {
  try {
    // Check if path exists and is a file
    const stats = fs.statSync(filePath);
    if (!stats.isFile()) {
      return `Error: Path ${filePath} is not a file`;
    }
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    if (error instanceof Error) {
      return `Error reading file ${filePath}: ${error.message}`;
    }
    return `Error reading file ${filePath}: Unknown error`;
  }
}

export function getProjectStructure(dirPath: string, currentDepth: number = 1): FileNode[] {
  const MAX_DEPTH = 3;
  const items = fs.readdirSync(dirPath);
  const result: FileNode[] = [];

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stats = fs.statSync(fullPath);

    // Skip node_modules and hidden directories/files
    if (item === 'node_modules' || item.startsWith('.')) {
      continue;
    }

    if (stats.isDirectory()) {
      const dirNode: FileNode = {
        name: item,
        type: 'directory',
        depth: currentDepth,
        children: currentDepth < MAX_DEPTH ? getProjectStructure(fullPath, currentDepth + 1) : []
      };
      result.push(dirNode);
    } else {
      result.push({
        name: item,
        type: 'file',
        depth: currentDepth
      });
    }
  }

  return result;
}

export async function searchCodeBase(query: string, targetDirectory: string[] = []) {
  const searchManager = new SearchManager();
  const searchResult = await searchManager.search({
    query,
    topK: 10,
    targetDirectory: targetDirectory || [],
    chatHistory: [],
    enable_rewrite: false,
    username: GlobalConfig.getConfig().getUsername(),
    gitRepo: GlobalConfig.getConfig().getRepoPath(),
    dirPath: GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd()
  });
  return new Set(
    searchResult.code_context_list.map((item) => {
      return {
        code_content: item.code_content,
        file_path: item.metadata.file_path
      };
    })
  );
}

export function writeFile(filePath: string, content: string): { success: boolean; error?: string } {
  try {
    if (!filePath) {
      return { success: false, error: 'filePath is required' };
    }
    if (!content) {
      return { success: false, error: 'content is required' };
    }
    logger.info('writeFile', filePath, content);
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );
    if (!fs.existsSync(prefixPath)) {
      fs.mkdirSync(prefixPath, { recursive: true });
    }
    fs.writeFileSync(path.join(prefixPath, filePath), content);
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return { success: false, error: `Failed to write file: ${errorMessage}` };
  }
}
