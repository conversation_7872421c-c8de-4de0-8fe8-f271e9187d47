import { ChatMessage } from './types';

// 共用的项目分析模板
export const PROJECT_OVERVIEW_TEMPLATE = `# Project Overview - [PROJECT_NAME]

## 📌 Purpose

[PROJECT_NAME] is a [system type, e.g., "multi-tenant admin dashboard"] designed to support [business goal], such as [e.g., "user onboarding, workflow automation, and analytics"].

## 🧩 Core Business Features

- Feature A: [Brief explanation of a core business flow]
- Feature B: [Another distinct capability that reflects business logic]
- Feature C: [Supporting or adjacent functionality, e.g., notifications, export]

## 👥 User Roles / Actors

- **[Role1]**: [Permission scope or responsibility]
- **[Role2]**: [Permission scope or responsibility]
- **[Role3]** (optional): [Permission scope or responsibility]

## 🧱 Module Overview

\`\`\`mermaid
graph TD
  A[Frontend App] --> B[Backend API]
  B --> C[Auth Module]
  B --> D[Business Logic Layer]
  D --> E[Data Access Layer]
  E --> F[(Database)]
\`\`\`

`;

export const ARCHITECTURE_TEMPLATE = `# Architecture and Code Structure - [PROJECT_NAME]

## 🏗️ Technology Stack

- Frontend: [e.g., React + Vite + TailwindCSS]
- Backend: [e.g., Node.js + Koa + TypeORM]
- Infra: [e.g., PostgreSQL, Redis, Docker, CI/CD with GitHub Actions]

## 🧭 System Modules

\`\`\`mermaid
graph TD
  A[Client App] -->|REST/GraphQL| B[API Server]
  B --> C[Module A - e.g., User Management]
  B --> D[Module B - e.g., Workflow Engine]
  B --> E[Module C - e.g., Notification Queue]
\`\`\`

## Directory Overview

/ ── apps/
    ├── frontend/              # Web interface
    └── backend/               # API server
        ├── modules/
        │   ├── auth/
        │   ├── orders/
        │   └── ...
        └── utils/
        
## Key Design Patterns

[e.g., CQRS separation between command/query logic]
[e.g., Domain-driven module boundaries]
`;

export const DEVELOPMENT_GUIDE_TEMPLATE = `
# Development Guide - [PROJECT_NAME]

## 🚀 Getting Started

\`\`\`bash
# Install dependencies
yarn install

# Copy environment variables
dotenv cp .env.example .env

# Start development server
yarn dev
\`\`\`

## Local Environment Notes
Node version: [x.x.x]

Required services: [e.g., PostgreSQL, Redis, etc.]

Ports used: [3000 for frontend, 4000 for backend]

Package Manager: pnpm v7.2.0

## Build & Deploy

### Build frontend
yarn workspace frontend build

### Build backend
yarn workspace backend build

### Run all in Docker
docker-compose up --build

## Testing

Unit tests: yarn test

Lint: yarn lint

Coverage: yarn coverage

## Troubleshooting

Common Error: [e.g., Migration failed → Solution]
Docker won't start → [Fix: remove cache volume]
`;

// Combined template for comprehensive project analysis
export const PROJECT_ANALYSIS_TEMPLATE = `
# Project Analysis Report - [PROJECT_NAME]

## 📌 Project Overview

### Purpose
[PROJECT_NAME] is a [system type] designed to [business goal and main functionality].

### Core Business Features
- Feature 1: [Description]
- Feature 2: [Description]
- Feature 3: [Description]

### User Roles
- **Role 1**: [Permissions and responsibilities]
- **Role 2**: [Permissions and responsibilities]

## 🏗️ Architecture & Technology Stack

### Technology Stack
- Backend: [frameworks, languages, versions]
- Frontend: [frameworks, libraries, tools]
- Database: [type, version, key configurations]
- Infrastructure: [deployment, CI/CD, monitoring]

### System Architecture
\`\`\`mermaid
graph TD
  [Architecture diagram based on actual code analysis]
\`\`\`

### Directory Structure
\`\`\`
[Actual project directory tree with explanations]
\`\`\`

## 🔧 Key Modules & Components

### Core Modules
[Detailed analysis of each major module with responsibilities]

### Data Flow
\`\`\`mermaid
sequenceDiagram
  [Actual data flow based on code analysis]
\`\`\`

## 🚀 Development & Deployment

### Getting Started
\`\`\`bash
[Actual commands from package.json and documentation]
\`\`\`

### Environment Setup
[Actual environment requirements and configuration]

### Build & Deploy Process
[Actual build scripts and deployment procedures]

## 📝 File Documentation
[Detailed documentation of key files, their purposes, and relationships]
`;

export const TECH_STACK_AND_PREFERENCES_TEMPLATE = `
# Tech Stack & Engineering Preferences - [PROJECT_NAME]

## 📦 Key npm Dependencies

> These reflect the foundational dependencies used across the project.

### Runtime

{
  "express": "^4.18.2",
  "jsonwebtoken": "^9.0.0",
  "pg": "^8.11.1",
  "redis": "^4.6.7",
  "axios": "^1.6.7"
}

### Dev / Tooling

{
  "typescript": "^5.4.0",
  "eslint": "^8.56.0",
  "prettier": "^3.2.5",
  "nodemon": "^3.0.3",
  "jest": "^29.7.0"
}

### Monorepo / Workspace (if applicable)

{
  "turbo": "^1.14.2",
  "pnpm": "^9.1.2"
}

## ✅ Engineering Conventions

- Language: [TypeScript preferred for all services]
- Naming: [camelCase for JS, snake_case for DB]
- Commit messages: [Conventional Commits]
- Styling: [Prettier + ESLint config shared across repos]

## 🧠 Technical Rationale (Optional)

- Chose [Framework] because [e.g., "modular DI architecture"]
- Prefer monorepo for unified dependency + deployment management
- Use gRPC for internal services due to [e.g., schema enforcement & perf]
`;

// System prompt for generating comprehensive documentation
export const getDocumentationGeneratorMessages = (dirPath: string): ChatMessage[] => [
  {
    role: 'system',
    content: `You are a Technical Documentation Generator specialized in creating comprehensive project documentation for codebases.

TASK OBJECTIVE:
Generate FOUR comprehensive documentation files for the project at ${dirPath}:

1. PROJECT_OVERVIEW.md - Project Overview
2. ARCHITECTURE.md - Architecture & Modules  
3. DEVELOPMENT_GUIDE.md - Development & Operation Guide
4. TECH_STACK_AND_PREFERENCES.md - Technology Stack & Engineering Preferences

ANALYSIS REQUIREMENTS:
1. Deeply analyze project structure to understand core business logic
2. Identify key modules and component responsibilities
3. Analyze technology stack and dependency relationships
4. Understand build and deployment processes
5. Discover user roles and permission models
6. Identify core business features and use cases

DOCUMENTATION TEMPLATES:

=== 1. PROJECT_OVERVIEW.md Template ===
${PROJECT_OVERVIEW_TEMPLATE}

=== 2. ARCHITECTURE.md Template ===
${ARCHITECTURE_TEMPLATE}

=== 3. DEVELOPMENT_GUIDE.md Template ===
${DEVELOPMENT_GUIDE_TEMPLATE}

=== 4. TECH_STACK_AND_PREFERENCES.md Template ===
${TECH_STACK_AND_PREFERENCES_TEMPLATE}

AVAILABLE ANALYSIS FUNCTIONS:
You have access to the following specialized functions for project analysis:

1. **getFileContent(filePath: string)**: 
   - Reads and returns the content of any file in the project
   - Use this to examine key files like package.json, README.md, config files, and source code
   - Returns file content as string or error message if file doesn't exist

2. **getProjectStructure(dirPath: string, currentDepth?: number)**:
   - Returns the complete directory structure as a tree (max depth: 3 levels)
   - Automatically excludes node_modules and hidden files/directories
   - Returns FileNode[] with name, type (file/directory), depth, and children
   - Use this to understand the overall project organization

3. **searchCodeBase(dirPath: string, query: string, targetDirectory?: string[])**:
   - Performs semantic search across the entire codebase using embeddings
   - Returns relevant code snippets and file paths based on your query
   - Use natural language queries like "authentication logic", "database connections", "API endpoints"
   - Can optionally target specific directories for focused search
   - Returns Set of {code_content, file_path} objects

4. **writeFile(filePath: string, content: string)**:
   - Writes content to a file (for generating the documentation files)
   - Returns {success: boolean, error?: string}
   - Use this to create the final PROJECT_OVERVIEW.md, ARCHITECTURE.md, and DEVELOPMENT_GUIDE.md files

GENERATION RULES:
1. **MANDATORY**: Use getProjectStructure() first to understand the overall project layout
2. **MANDATORY**: Use getFileContent() to read key files like package.json, README.md, main entry points
3. **RECOMMENDED**: Use searchCodeBase() with semantic queries to find specific functionality:
   - "main application entry point"
   - "database schema and models"
   - "API routes and endpoints"
   - "authentication and authorization"
   - "configuration and environment setup"
   - "build and deployment scripts"
4. **MANDATORY**: Use writeFile() to generate each of the four documentation files
5. Fill all template placeholders based on actual code content from your analysis
6. Ensure all descriptions are accurate and meaningful
7. Include actual code examples and configuration details from the files you read
8. Provide complete contextual information based on your findings
9. Generated content must be based on code evidence, no false information
10. Use multiple searchCodeBase() queries with different keywords to get comprehensive coverage

OUTPUT FORMAT:
Generate complete markdown content for each file, with clear separators marking the start and end of each file.

Start analyzing the project and generate four complete documentation files.`
  },
  {
    role: 'user',
    content: `Analyze the project at ${dirPath} and generate comprehensive documentation:

STEP 1: PROJECT ANALYSIS
1. Use getProjectStructure("${dirPath}") to understand the overall project layout
2. Use getFileContent() to read key files:
   - package.json (dependencies, scripts, project info)
   - README.md (existing documentation)
   - src/index.ts or main entry points
   - Configuration files (tsconfig.json, etc.)
3. Use searchCodeBase() with targeted queries to find:
   - Core business logic and features
   - Architecture patterns and design
   - Database and data layer code
   - API endpoints and services
   - Authentication and security
   - Build and deployment configuration

STEP 2: DOCUMENTATION GENERATION
Generate these four files using writeFile():
1. PROJECT_OVERVIEW.md - Complete project overview documentation
2. ARCHITECTURE.md - Architecture and module documentation  
3. DEVELOPMENT_GUIDE.md - Development and operation guide
4. TECH_STACK_AND_PREFERENCES.md - Technology stack and engineering preferences

REQUIREMENTS:
- All content must be based on actual code analysis using the provided functions
- Replace all template placeholders with real project information
- Include actual code snippets and configuration examples
- Provide accurate dependency versions and script commands
- Document real module relationships and data flows

Start with the analysis phase, then generate the documentation files.`
  }
];
// Separate messages for analysis and supervision
export const getAnalysisMessages = (dirPath: string): ChatMessage[] => [
  {
    role: 'system',
    content: String.raw`You are a Project Analysis Assistant specialized in providing comprehensive, evidence-based documentation of codebases.

LANGUAGE REQUIREMENT:
- All responses MUST be in Chinese (Simplified Chinese)
- Keep technical terms in English when necessary (e.g. API, Framework names, File paths)
- Error messages and warnings must also be in Chinese

CODE SNIPPET RULES:
- ALL code examples MUST be 5 lines or less
- If implementation is longer, show the most critical 5 lines
- For longer implementations:
  * Provide file location for complete code
  * Break down into multiple 5-line segments if critical
- Focus on the most representative code sections

CORE RESPONSIBILITIES:
1. Project Structure Analysis
2. Implementation Discovery
3. Pattern Recognition
4. Evidence Collection
5. Documentation Generation
6. Configuration Documentation

PROJECT ANALYSIS TEMPLATE:
${PROJECT_ANALYSIS_TEMPLATE}

OUTPUT REQUIREMENTS:
1. All content must be based on code evidence
2. Maintain clear and hierarchical structure
3. Highlight key modules and features
4. Include actual code examples and configuration details
5. Provide complete context information
6. Replace all bracketed placeholders with actual content
7. Ensure all descriptions are accurate and meaningful

ANALYSIS RULES:
1. use functions as much as possible to analyze the project
2. read the code as much as possible to get the full picture of the project
3. read the structure of the project to understand the project
4. document each key file with detailed descriptions and call relationships
5. create comprehensive dependency maps between components
6. identify and document the primary execution flows
`
  },
  {
    role: 'user',
    content: `Analyze the project at ${dirPath} and provide:
1. A comprehensive project analysis
2. A complete project_config.md following the specified format
3. Document each key file's purpose, content, and relationships
4. Map out the key execution flows and dependencies
5. remove all the message that is not sure to be correct`
  }
];

export const getSupervisionMessages = (analysis: string): ChatMessage[] => [
  {
    role: 'system',
    content: `You are a Project Analysis Supervisor. Your role is to ensure the analysis is comprehensive, accurate, and actionable.

VALIDATION CRITERIA:
1. Architecture Analysis
   - 模块划分完整性
   - 职责边界清晰性
   - 依赖关系合理性
   - 扩展性设计评估

2. Implementation Details
   - 核心功能覆盖度
   - 技术选型合理性
   - 代码结构规范性
   - 性能优化方案

3. Documentation Quality
   - 文档结构完整性
   - 技术描述准确性
   - 示例代码实用性
   - 配置说明清晰度
   - 文件级别文档完整性
   - 调用关系映射准确性

4. Best Practices
   - 设计模式应用
   - 代码规范遵循
   - 安全实践落实
   - 测试覆盖情况

5. Integration & Deployment
   - 环境配置完整性
   - 部署流程清晰度
   - 依赖管理规范性
   - 监控方案合理性

6. File Documentation
   - 每个关键文件都有详细描述
   - 文件间依赖关系清晰
   - 组件调用流程完整
   - 核心功能的执行路径明确

7. remove all the message that is not sure to be correct

Document Format:
${PROJECT_ANALYSIS_TEMPLATE}

RESPONSE FORMAT:
## Summary
One sentence technical assessment

## Improvements
1. {Title}
   - Issue: {gap in current analysis}
   - Fix: {specific action steps}

2. {Title}
   - Issue: {gap in current analysis}
   - Fix: {specific action steps}

3. {Title}
   - Issue: {gap in current analysis}
   - Fix: {specific action steps}

If no improvements needed, respond with exactly: "ALLDONE"`
  },
  {
    role: 'user',
    content: `Review and provide improvement suggestions, If no improvements needed, respond with exactly: "ALLDONE"

${analysis}
`
  }
];
