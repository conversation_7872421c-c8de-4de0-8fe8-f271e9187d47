import { callLLM } from '@/http/llm';
import { getFileContentSchema, getProjectStructureSchema, searchCodeBaseSchema, writeFileSchema } from './schema';
import { AssistantMessage, ToolCall, ProjectInfoDetail } from './types';
import { getProjectStructure, getFileContent, searchCodeBase, writeFile } from './functions';
import { getDocumentationGeneratorMessages } from './prompt';
import { getKwaipilotGlobalPath, md5 } from '@/util/paths';
import { GlobalConfig } from '@/util/global';
import fs from 'fs';
import path from 'path';
import { Logger } from '@/util/log';
import { IMessenger } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
const logger = new Logger('project-wiki');

let globalState = false;
let isGenerating = false;
export const initProjectWiki = async (
  dirPath: string,
  messager: I<PERSON><PERSON>enger<ToCoreProtocol, FromCoreProtocol>,
  force: boolean = false
) => {
  const sendProgress = (progress: number, message: string) => {
    messager.send('wiki/generateProgress', {
      progress,
      message
    });
  };
  try {
    if (isGenerating) {
      return;
    }
    isGenerating = true;
    const exist = await checkProjectWiki();
    if (exist && !force) {
      return;
    }
    let iterationCount = 0;
    const MAX_ITERATIONS = 5;
    let finalAnalysis = '';
    const readFileList: string[] = [];
    const analysisMessages = getDocumentationGeneratorMessages(dirPath);
    const structrueList: string[] = [];
    const codeBaseSearchList: string[] = [];

    let progress = 0.01;
    sendProgress(progress, 'Project Wiki ...');
    while (iterationCount < MAX_ITERATIONS) {
      // Step 1: Get initial analysis
      let { data: currentResponse } = await callLLM(analysisMessages, [
        getProjectStructureSchema,
        getFileContentSchema,
        searchCodeBaseSchema,
        writeFileSchema
      ]);
      progress = progress <= 0.7 ? Math.min(progress + 0.05, 0.7) : progress;
      sendProgress(progress, 'Project Wiki ...');
      while (currentResponse.tool_calls) {
        // 构建助手消息对象
        const assistantMessage: AssistantMessage = {
          role: 'assistant',
          content: currentResponse.content,
          tool_calls: currentResponse.tool_calls
        };
        analysisMessages.push(assistantMessage);
        logger.info('添加助手消息到历史:', JSON.stringify(assistantMessage, null, 2));

        const functionResults = await Promise.all(
          currentResponse.tool_calls.map(async (toolCall: ToolCall) => {
            const functionName = toolCall.name;
            const functionArgs = toolCall.input;
            let result;
            logger.info('执行工具调用:', {
              id: toolCall.id,
              name: functionName,
              args: functionArgs
            });

            if (functionName === 'getProjectStructure') {
              result = getProjectStructure(functionArgs.dirPath);
              structrueList.push(JSON.stringify(result));
            } else if (functionName === 'getFileContent') {
              readFileList.push(functionArgs.filePath);
              result = getFileContent(functionArgs.filePath);
            } else if (functionName === 'searchCodeBase') {
              codeBaseSearchList.push(functionArgs.query);
              result = await searchCodeBase(functionArgs.query, functionArgs.targetDirectory);
            } else if (functionName === 'writeFile') {
              result = writeFile(functionArgs.filePath, functionArgs.content);
              progress = Math.min(progress + 0.1, 0.95);
              sendProgress(progress, 'Project Wiki ...');
            }

            const toolResult = {
              role: 'tool' as const,
              tool_call_id: toolCall.id,
              content: typeof result === 'string' ? result : JSON.stringify(result)
            };

            logger.info('工具调用结果:', {
              tool_call_id: toolResult.tool_call_id,
              tool_name: toolCall.name,
              contentLength: toolResult.content.length
            });

            return toolResult;
          })
        );

        analysisMessages.push(...functionResults);
        logger.info('当前消息历史长度:', analysisMessages.length);
        logger.info(
          '最后两条消息类型:',
          analysisMessages.slice(-2).map((msg) => ({
            role: msg.role,
            hasToolCalls: 'tool_calls' in msg && (msg.tool_calls?.length ?? 0) > 0,
            hasToolCallId: 'tool_call_id' in msg
          }))
        );

        logger.info(
          '发送给LLM的消息历史:',
          JSON.stringify(
            analysisMessages.map((msg) => ({
              role: msg.role,
              content: msg.content ? msg.content.substring(0, 100) + '...' : undefined,
              tool_calls: 'tool_calls' in msg ? msg.tool_calls?.length : undefined,
              tool_call_id: 'tool_call_id' in msg ? msg.tool_call_id : undefined
            })),
            null,
            2
          )
        );

        const { data: nextResponse } = await callLLM(analysisMessages, [
          getProjectStructureSchema,
          getFileContentSchema,
          searchCodeBaseSchema,
          writeFileSchema
        ]);
        currentResponse = nextResponse;
        progress = progress <= 0.6 ? Math.min(progress + 0.03, 0.6) : progress;
        sendProgress(progress, 'Project Wiki ...');
      }

      const analysis = currentResponse.content;
      if (!analysis) continue;
      logger.info('analysis -- structrue', analysis);
      // 如果是最后一次则不需要再进行分析了。
      iterationCount++;
      if (iterationCount === MAX_ITERATIONS) {
        finalAnalysis = analysis;
        logger.info('Reached maximum iterations, returning last analysis');
        break;
      }
      // // Step 2: Get supervision feedback
      // const supervisionMessages = getSupervisionMessages(
      //   analysis +
      //     `\nthe analysis is based on the project structure of: ${JSON.stringify(
      //       structrueList
      //     )}, and the file content of ${readFileList.join(
      //       ', '
      //     )} and the code base search result of ${codeBaseSearchList.join(', ')}`
      // );
      //     const { data: supervisionResponse } = await callLLM(supervisionMessages, []);
      //     console.log('supervisionResponse', supervisionResponse.content);

      //     if (supervisionResponse.content?.includes('ALLDONE')) {
      //       finalAnalysis = analysis;
      //       break;
      //     }

      //     // Step 3: If not validated, add supervision feedback and try again
      //     analysisMessages.push({
      //       role: 'user',
      //       content: `Please improve the analysis by searching the code base and the file content or project structure based on this feedback:

      // ${supervisionResponse.content}

      // Focus on providing more concrete evidence and examples from the actual codebase. ${
      //         iterationCount === MAX_ITERATIONS - 1
      //           ? 'Please ensure the analysis is complete and well-validated. remove all unneccesary content such as "Based on the project structure: xxx, and the file content of xxx and the code base search result of xxx" "file not found" "no implementation found" and reply in Chinese. Follow the template of ' +
      //             PROJECT_ANALYSIS_TEMPLATE
      //           : ''
      //       }`
      //     });
    }
    sendProgress(1, 'Project Wiki generate success');
    isGenerating = false;
    return finalAnalysis;
  } catch (error) {
    logger.error('Error executing index tasks---2222:', {
      error: error,
      context: 'executeIndexTasks',
      timestamp: new Date().toISOString()
    });
    sendProgress(0, 'Project Wiki generate error');
    isGenerating = false;
  }
};

export const checkProjectWiki = async () => {
  const requiredFiles = [
    'ARCHITECTURE.md',
    'DEVELOPMENT_GUIDE.md',
    'PROJECT_OVERVIEW.md',
    'TECH_STACK_AND_PREFERENCES.md'
  ];
  if (globalState) {
    return true;
  }
  try {
    // 构建projectWiki文件夹路径，使用与writeFile相同的逻辑
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );

    // 检查projectWiki文件夹是否存在
    if (!fs.existsSync(prefixPath)) {
      return false;
    }

    // 检查每个必需的文件
    for (const fileName of requiredFiles) {
      const filePath = path.join(prefixPath, fileName);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return false;
      }

      // 检查文件内容是否为空
      try {
        const content = fs.readFileSync(filePath, 'utf8').trim();
        if (content.trim().length === 0) {
          return false;
        }
      } catch (error) {
        // 如果读取文件失败，返回false
        return false;
      }
    }
    globalState = true;
    // 所有文件都存在且内容不为空
    return true;
  } catch (error) {
    // 如果发生任何错误，返回false
    logger.error('检查项目信息文件时发生错误:', error);
    return false;
  }
};

export const getProjectWiki = async (): Promise<ProjectInfoDetail | null> => {
  const exist = await checkProjectWiki();
  if (!exist) {
    return null;
  }

  try {
    const requiredFiles = [
      'ARCHITECTURE.md',
      'DEVELOPMENT_GUIDE.md',
      'PROJECT_OVERVIEW.md',
      'TECH_STACK_AND_PREFERENCES.md'
    ];
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );

    const projectInfo: Record<string, string> = {};

    // 读取每个文件的内容
    for (const fileName of requiredFiles) {
      const filePath = path.join(prefixPath, fileName);
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileKey = fileName.replace('.md', '').toLowerCase();
        projectInfo[fileKey] = content;
      } catch (error) {
        logger.error(`读取文件 ${fileName} 时发生错误:`, error);
        projectInfo[fileName.replace('.md', '').toLowerCase()] = '';
      }
    }

    return {
      architecture: projectInfo.architecture || '',
      development_guide: projectInfo.development_guide || '',
      project_overview: projectInfo.project_overview || '',
      tech_stack_and_preferences: projectInfo.tech_stack_and_preferences || '',
      path: prefixPath,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    logger.error('获取项目信息详情时发生错误:', error);
    return null;
  }
};
export const deleteProjectWiki = async () => {
  try {
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );
    if (fs.existsSync(prefixPath)) {
      fs.rmSync(prefixPath, { recursive: true, force: true });
      globalState = false;
    }
    return true;
  } catch (error) {
    logger.error('删除项目信息时发生错误:', error);
    return false;
  }
};
