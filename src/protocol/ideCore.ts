import { InstallMcpParams, MarketMcpDetail, McpServer, McpServerChangeEventDetail } from '@/mcp/types';
import {
  CommandStatusCheckResponse,
  DiffSet,
  EditFileRequest,
  EditFileResponse,
  ExecuteCommandResponse,
  LocalMessage,
  MessageParam,
  WebviewMessage
} from '@/agent/types/type';
import type { IdeSettings, SearchSearchParams, ResponseBase, RepoInfo } from './index.d';
import { RepoStatusEnum } from '@/db/sqlite/tables/repo-status';
export enum IndexAction {
  INDEX_FILE = 'INDEX_FILE',
  PROCESS_FILE = 'PROCESS_FILE',
  ERROR = 'ERROR'
}

export type ToIdeFromCoreProtocol = {
  'state/updatePort': [
    {
      port: number;
      host?: string;
    },
    void
  ];
  'state/ideState': [undefined, ResponseBase<null>];
  'config/getIdeSetting': [undefined, ResponseBase<IdeSettings>];
  'index/progress': [
    {
      progress: number;
      total: number;
      done: number;
      filepath: string;
      action: IndexAction;
      message?: string;
    },
    undefined
  ];
  'state/sendNotification': [
    {
      type: 'error' | 'warning' | 'info';
      name: string;
      message: string;
    },
    undefined
  ];
  'state/ideInfo': [
    undefined,
    ResponseBase<{
      pluginVersion: string;
      version: string;
      platform: IdePlatform;
      cwd: string;
      repoInfo: RepoInfo;
      kwaipilotLanguage: string;
      userInfo: {
        name: string;
      };
      proxyUrl: string;
      jwtToken?: string;
      versionType: 'External' | 'Internal';
      maxIndexSpace: number;
      device?: IdeDevice;
      language: string;
    }>
  ];
  'mcp/mcpServerChange': [
    {
      /** 是否发生错误 */
      isError: boolean;
      /** 错误代码 */
      code: number;
      /** 事件消息 */
      message: string;
      /** 当前所有MCP服务器列表 */
      mcpServers: McpServer[];
    },
    undefined
  ];
  'assistant/agent/message': [LocalMessage, any];
  'assistant/agent/messageList': [LocalMessage[], any];
  'assistant/agent/apiConversationList': [MessageParam[], any];
  'assistant/agent/environment': [{ includeFileDetails: boolean }, ResponseBase<string>];
  'assistant/agent/figmaToken': [{}, ResponseBase<string>];
  'assistant/agent/environmentV2': [
    undefined,
    ResponseBase<{
      visibleFiles: string[];
      openTabs: string[];
    }>
  ];
  'assistant/agent/executeCommand': [
    { command: string; is_background?: boolean; ignore_output?: boolean },
    ResponseBase<ExecuteCommandResponse>
  ];
  'assistant/agent/editFile': [EditFileRequest, ResponseBase<EditFileResponse>];

  // 预览
  'uiPreview/info': [{
    type: string; // 'error' | 'element' | 'log' | 'warn' | 'info' | 'image' ,
    displayName: string;
    data: string; // '提示词拼接后的 xml 字符串'
    desc?: string[] | string;
    focusInfo?: { // dom元素定位需要的信息
      filePath?: string;
      start?: {
        line?: number;
        column?: number;
      };
    }
  }, undefined];
  // 预览 - 页面刷新
  'uiPreview/refresh': [undefined, undefined];

  'assistant/agent/commandStatusCheck': [{ check_duration: number }, ResponseBase<CommandStatusCheckResponse>];
  'wiki/generateProgress': [
    {
      progress: number;
      message?: string;
    },
    undefined
  ];
  'assistant/agent/writeToFile': [{ path: string; diff?: string, content: string; newFile: boolean; sessionId?: string; chatId?: string }, ResponseBase<EditFileResponse>];
};

export type IdePlatform = 'xcode' | 'vscode' | 'jetbrains';
export type IdeDevice = 'kwaipilot-vscode' | 'kwaipilot-xcode' | 'kwaipilot-intellij';
export type ToCoreFromIdeProtocol = {
  // 健康检查接口
  'state/agentState': [undefined, ResponseBase<null>];

  // 索引相关接口
  'index/file': [{ file: string; action: 'modify' | 'delete' | 'create' }, ResponseBase<null>];
  'index/build': [{ shouldClearIndexes?: boolean }, ResponseBase<boolean>];
  'index/pause': [undefined, ResponseBase<boolean>];
  'index/clearIndex': [undefined, ResponseBase<boolean>];
  'index/repoIndex': [{ manual?: boolean } | undefined, ResponseBase<boolean>];
  'index/indexedFileList': [undefined, ResponseBase<boolean>];

  // 状态相关接口
  'state/checkRepoState': [
    undefined,
    ResponseBase<{
      id: number;
      repo: string;
      repoPath: string;
      branch: string;
      commitId: string;
      lastUpdateTime: number;
      createTime: number;
      total: number;
      done: number;
      progress: number;
      isBuilding: boolean;
      status: RepoStatusEnum;
      isPaused: boolean;
      message: string;
    } | null>
  ];

  // 搜索相关接口
  'search/search': [SearchSearchParams, ResponseBase<any>];

  // MCP相关接口
  'mcp/getSettingsPath': [undefined, ResponseBase<string>];
  'mcp/getAllMcpServers': [undefined, ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>];
  'mcp/toggleMcpServer': [
    {
      serverName: string;
      disabled: boolean;
    },
    ResponseBase<boolean>
  ];
  'mcp/restartMcpServer': [
    {
      serverName: string;
    },
    ResponseBase<boolean>
  ];
  'mcp/deleteMcpServer': [
    {
      serverName: string;
    },
    ResponseBase<boolean>
  ];
  'mcp/installMcp': [InstallMcpParams, ResponseBase<boolean>];
  'mcp/fetchAvailableMcpListByMarket': [
    {
      page?: number;
      pageSize?: number;
      searchKeyword?: string;
    },
    ResponseBase<{
      page?: number;
      pageSize?: number;
      total?: number;
      records?: MarketMcpDetail[];
    }>
  ];
  'mcp/fetchMcpDetailByMarket': [{ serverId: string }, ResponseBase<MarketMcpDetail>];

  // 助理模式agent相关接口
  'assistant/agent/local': [WebviewMessage, any];
  'assistant/agent/getDiffSet': [{ sessionId: string; lhsHash: string; rhsHash?: string }, ResponseBase<DiffSet[]>];
  'rules/getRulesList': [{ rules: string[] }, ResponseBase<(string | undefined)[]>];
  'state/userLogin': [{ username: string }, ResponseBase<null>];

  // UI预览相关接口
  'uiPreview/previewBrowser': [{ url: string }, ResponseBase<{ url: string }>],
  'uiPreview/previewProxy': [{ url: string }, ResponseBase<{ proxyUrl: string, url: string }>],
  'uiPreview/installBrowser': [undefined, ResponseBase<null>],
  'uiPreview/checkPortActive': [{ url: string }, ResponseBase<boolean>],

  'state/jwtToken': [{ jwtToken?: string }, ResponseBase<null>];
  // 项目信息
  'wiki/getProjectWiki': [undefined, ResponseBase<any>];
};

// IDE
export type ToIdeProtocol = ToIdeFromCoreProtocol;
export type FromIdeProtocol = ToCoreFromIdeProtocol;

// Core
export type ToCoreProtocol = ToCoreFromIdeProtocol;
export type FromCoreProtocol = ToIdeFromCoreProtocol;
