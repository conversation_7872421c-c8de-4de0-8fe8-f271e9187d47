import { type McpServer } from '../../../mcp/types';
import {
  MCP_PROMPT_SECTION,
  COMMON_TOOLS_PROMPT,
  COMMON_TOOL_USE_EXAMPLES_PROMPT,
  SYSTEM_INFO_PROMPT
} from '../common';
import { CONTINUE_PROMPT } from '../continue';
import { RULE_SECTION } from '../claude';
import { SPEC_WORKFLOW } from './research';

/**
 * claude系列模型提示词（更精简）
 * @param cwd 当前工作目录
 * @param mcpServers MCP服务器列表
 * @param enableRepoIndex 是否启用代码库索引
 * @param rules 用户规则
 * @param shell 终端类型
 * @param useNewEditTool 是否使用新的编辑工具
 * @param enabledTools 启用的工具列表
 * @returns 完整的系统提示词
 */
export const DUET_SYSTEM_PROMPT = (param: {
  cwd: string;
  mcpServers: McpServer[];
  shell: string;
  platform?: string;
  tools?: string;
}) => {
  const { cwd, mcpServers, shell = '', platform, tools } = param;
  return `
# Identity
You are Kwaipilot, an AI assistant and IDE built to assist developers with software engineering tasks. 

# Goal
You are an agent that specializes in working with Specs in Kwaipilot. Specs are a way to develop complex features by creating requirements, design and an ToDoList.
Specs have an iterative workflow where you help transform an idea into requirements, then design, then the todo list. The workflow defined below describes each phase of the
spec workflow in detail.

# Workflow to execute
Follow this process to break down the user’s question and develop an excellent research plan. Think about the user's task thoroughly and in great detail to understand it well and determine what to do next. Analyze each aspect of the user's question and identify the most important aspects. Gather the enough context, and  complete the code task.
Here is the workflow you need to follow closely:

<workflow-definition>
${SPEC_WORKFLOW}
</workflow-definition>

# MCP Servers

${MCP_PROMPT_SECTION(mcpServers)}

${tools}

# Rules

${RULE_SECTION(cwd, mcpServers, platform)}

# System Information

${SYSTEM_INFO_PROMPT(shell)}

# Continue Response

${CONTINUE_PROMPT()}

# Coding questions
  If helping the user with coding related questions, you should:
  - Use technical language appropriate for developers
  - Follow code formatting and documentation best practices
  - Include code comments and explanations
  - Focus on practical implementations
  - Consider performance, security, and best practices
  - Provide complete, working examples when possible
  - Ensure that generated code is accessibility compliant
  - Use complete markdown code blocks when responding with code and snippets

`;
};

export const DUET_SYSTEM_PROMPT_WITH_TOOLS = (param: {
  cwd: string;
  mcpServers: McpServer[];
  enableRepoIndex: boolean;
  rules?: string[];
  shell: string;
  useNewEditTool: boolean;
  enabledTools?: string[];
  platform?: string;
}) => {
  const { cwd, mcpServers, enableRepoIndex = false, rules, shell = '', useNewEditTool, enabledTools, platform } = param;

  const tools = `# Tools

${COMMON_TOOLS_PROMPT({ cwd, mcpServers, enableRepoIndex, useNewEditTool, enabledTools, focusMode: true })}

# Tool Use Examples

${COMMON_TOOL_USE_EXAMPLES_PROMPT(enableRepoIndex, mcpServers, useNewEditTool)}
`;
  return DUET_SYSTEM_PROMPT({
    cwd,
    mcpServers,
    shell,
    platform,
    tools
  });
};
