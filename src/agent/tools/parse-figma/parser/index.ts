import { GetFileNodesResponse, Style } from '@figma/rest-api-spec';

import { figmaNodeTraverseAsync } from './traverse';
import { doUnitTask } from './unit-task';
import { tuningNodeLayout } from './tuning';
import { compressAltNode, transformComponet, transformComponetSet } from './utils';
import { fetchImageUrlForNodes } from './image-parse';
import type { ConversionFigmaJson, OneNodeWorkResult, FigmaNode, imageFetchConfig } from './types';

/**
 * 遍历执行的上下文
 */
export interface TraverseContext {
  imageNodes: string[];
  nodeRawMap: Record<string, FigmaNode>;
}

export interface ParseFigmaNodeOptions {
  /**
   * 图片URL注入配置
   */
  imageConfig?: imageFetchConfig;

  /**
   * Figma客户端实例（用于获取图片URL）
   */
  figmaClient?: any;
}

export async function parseFigmaNode(
  fileKey: string,
  nodeId: string,
  figmaJson: GetFileNodesResponse,
  options: ParseFigmaNodeOptions = {}
): Promise<ConversionFigmaJson> {
  const nodeIdStr = nodeId.includes('-') ? nodeId.replaceAll('-', ':') : nodeId;

  const { name, lastModified, nodes } = figmaJson;

  // 兼容处理 figma aa:bb 和 aa-bb 两种格式
  const { components, componentSets, styles, document } = nodes[nodeIdStr] || nodes[nodeId];

  const result: ConversionFigmaJson = {
    name,
    lastModified,
    nodes: undefined,
    components: transformComponet(components),
    componentSets: transformComponetSet(componentSets)
  };

  if (!document) {
    return result;
  }

  const traverseContext: TraverseContext = {
    imageNodes: [],
    nodeRawMap: {}
  };
  const initWorkResult: OneNodeWorkResult = {};

  const rootWorkResult = await figmaNodeTraverseAsync<OneNodeWorkResult>(
    document as FigmaNode,
    styles,
    async (
      curNode: FigmaNode,
      parentWorkResult: OneNodeWorkResult,
      parentNode: FigmaNode | undefined,
      styles: { [key: string]: Style },
      childIndex?: number,
    ) => {
      const taskResult = await doUnitTask(document, parentNode, curNode, styles, traverseContext, parentWorkResult, childIndex);

      return {
        ...taskResult,
        result: taskResult.result ?? {}
      };
    },
    initWorkResult,
    {
      batchSize: -1
    }
  );

  // 当用户把选中根节点标记为忽略节点时，转码结果为空
  if (!rootWorkResult?.node) {
    return result;
  }

  result.nodes = rootWorkResult.node;

  // 注入图片 URL
  if (options.figmaClient && traverseContext.imageNodes.length > 0) {
    try {
      await fetchImageUrlForNodes(
        result.nodes,
        traverseContext.imageNodes,
        fileKey,
        options.figmaClient,
        options.imageConfig
      );
    } catch (error) { }
  }

  result.nodes = tuningNodeLayout(result.nodes, traverseContext)?.nodes;

  compressAltNode(result.nodes);

  return result;
}
