import { Logger } from '@/util/log';

import { ComponentDefinition, NodeDefinition, imageFetchConfig } from './parser/types';
import { parseFigmaNode, ParseFigmaNodeOptions } from './parser';
import { FigmaClient } from './figma-request';
import { ASSISTANT_NAMESPACE } from '@/util/const';

const logger = new Logger('parse-figma-json');

const ERROR_MESSAGE = {
  NO_TOKEN: 'Figma API access token not provided. Please connnect figma in settings.',
  INVALID_URL: 'invalid figma url, please check the url'
}

function getErrorCode(message: string) {
  const REVERSE_ERROR_MAP = Object.entries(ERROR_MESSAGE).reduce((acc, [key, value]) => {
    acc[value] = key;
    return acc;
  }, {} as Record<string, string>);

  return REVERSE_ERROR_MAP[message] || null;
}

/**
 * 解析 Figma URL 提取关键信息
 */
function parseUrl(url: string) {
  // 支持的 Figma URL 模式
  const figmaUrlPatterns =
    /^https:\/\/www\.figma\.com\/file|design\/(?<fileKey>[a-zA-Z0-9]+)\/.+\?node-id=(?<nodeId>[a-zA-Z0-9]+[:-][a-zA-Z0-9]+)/;

  let fileKey: string | undefined = undefined;
  let nodeId: string | undefined = undefined;

  const match = url.match(figmaUrlPatterns);

  if (match) {
    fileKey = match.groups?.fileKey;
    nodeId = match.groups?.nodeId;
  }

  if (!fileKey || !nodeId) {
    throw new Error(ERROR_MESSAGE.INVALID_URL);
  }

  return {
    fileKey,
    nodeId,
    originalUrl: url,
    parsedAt: new Date().toISOString()
  };
}

/**
 * 解析 Figma URL 并返回包含设计数据的 JSON
 * @param url Figma 文件的 URL
 * @param parseConfig 解析配置
 * @returns 包含 URL 信息和设计数据的 JSON 对象
 */
export async function convertFigmaJson(
  url: string,
  options: {
    getTokenFn?: () => Promise<string>;
    imageConfig?: imageFetchConfig;
  }
): Promise<string> {
  const parseFigmaStartTime = Date.now();

  let fileKey = '';
  let nodeId = '';

  try {
    const urlInfo = parseUrl(url);

    fileKey = urlInfo.fileKey;
    nodeId = urlInfo.nodeId;

    logger.info(`[parse-figma] start parse figma url: ${url}`);

    const result: {
      nodes: NodeDefinition | undefined;
      components: Record<string, ComponentDefinition>;
      componentSets: Record<string, ComponentDefinition>;
      nodeImageUrl: string;
      fileKey: string; // 文件key
      nodeId: string; // 节点id
      metadata: Record<string, any>; // 元数据
      error?: string;
    } = {
      nodes: undefined,
      components: {},
      componentSets: {},
      nodeImageUrl: '',
      error: undefined,
      fileKey: '',
      nodeId: '',
      metadata: {}
    };

    const { getTokenFn, ...parseConfig } = options;

    // 通过调用 getEnvironmentFigmaToken 方法获取 figma token
    const figmaAccessToken = typeof getTokenFn === 'function' ? await getTokenFn() : '';

    if (!figmaAccessToken) {
      throw new Error(ERROR_MESSAGE.NO_TOKEN);
    }

    if (fileKey && nodeId) {
      logger.info(`[parse-figma] use figma api to get data, fileKey: ${fileKey}, nodeId: ${nodeId}`);

      const figmaClient = new FigmaClient(figmaAccessToken);

      const figmaNodeRes = await figmaClient.getFileNodes(fileKey, nodeId);

      console.log('figma原始数据', figmaNodeRes);

      logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'assistant/agent/getFigmaNodes',
        millis: Date.now() - parseFigmaStartTime,
        extra4: fileKey,
        extra6: nodeId
      });

      const parseOptions: ParseFigmaNodeOptions = {
        figmaClient: figmaClient,
        ...parseConfig
      };

      const parseStartTime = Date.now();
      const { nodes, components, componentSets, ...metadata } = await parseFigmaNode(
        fileKey,
        nodeId,
        figmaNodeRes,
        parseOptions
      );

      logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'assistant/agent/parseFigmaNode',
        millis: Date.now() - parseStartTime,
        extra4: fileKey,
        extra6: nodeId
      });

      // 获取根节点的图片（用于预览）
      try {
        const rootImageRes = await figmaClient.getImages(fileKey, nodeId);
        result.nodeImageUrl = rootImageRes.images?.[nodeId] ?? '';
      } catch (rootImageError) {
        logger.warn(`[parse-figma] get root node image failed: ${rootImageError}`);
      }

      result.nodes = nodes;
      result.components = components;
      result.componentSets = componentSets;
      result.fileKey = fileKey;
      result.nodeId = nodeId;
      result.metadata = metadata;

      logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'assistant/agent/parseFigmaSuccess',
        millis: Date.now() - parseFigmaStartTime,
        extra4: fileKey,
        extra6: nodeId
      });
    }

    // 返回格式化的 JSON 字符串
    return JSON.stringify(result, null, 2);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    logger.error(`[parse-figma] failed to parse figma url: ${errorMessage}`);

    const errorResult = {
      nodes: [],
      error: `Failed to get figma node data: ${errorMessage}`,
      fileKey: fileKey,
      nodeId: nodeId,
      metadata: {
        errorType: getErrorCode(errorMessage)
      }
    };

    logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'assistant/agent/parseFigmaError',
      millis: Date.now() - parseFigmaStartTime,
      extra4: fileKey,
      extra6: errorMessage
    });

    return JSON.stringify(errorResult, null, 2);
  }
}
