// 导入所有处理器类
import { Tool<PERSON>andlerContext, ToolHandler } from '../ToolHelpers';
import { EditFileHandler } from './EditFileHandler';
import { ReadFileHandler } from './ReadFileHandler';
import { ExecuteCommandHandler } from './ExecuteCommandHandler';
import { SearchAndReplaceHandler } from './SearchAndReplaceHandler';
import { WriteToFileHandler } from './WriteToFileHandler';
import { CodebaseSearchHandler } from './CodebaseSearchHandler';
import { ListFilesHandler } from './ListFilesHandler';
import { GrepSearchHandler } from './GrepSearchHandler';
import { UseMcpToolHandler } from './UseMcpToolHandler';
import { AskFollowupQuestionHandler } from './AskFollowupQuestionHandler';
import { CommandStatusCheckHandler } from './CommandStatusCheckHandler';
import { SearchWebHandler } from './SearchWebHandler';
import { FetchWebHandler } from './FetchWebHandler';
import { ProjectPreviewHandler } from './ProjectPreviewHandler';
import { ParseFigmaHandler } from './ParseFigmaHandler';
import { TodoWriteHandler } from './TodoWriteHandler';
import { SearchMemoryHandler } from './SearchMemoryHandler';
import { SaveMemoryHandler } from './SaveMemoryHandler';
import { GetMemoryHandler } from './GetMemoryHandler';
import { SearchSpecHandler } from './SearchSpecHandler';
import { RenderResearchPlanHandler } from './RenderResearchPlanHandler';
import { UpdatePhaseHandler } from './UpdatePhaseHandler';
import { BrowserActionHandler } from './BrowserActionHandler';
import { TestCaseHandler } from './TestCaseHandler';
import { TodoReadHandler } from './TodoReadHandler';

// 导出基础类型和接口
export { ToolHandlerContext, ToolHandler };

// 导出所有工具处理器类
export {
  EditFileHandler,
  ReadFileHandler,
  ExecuteCommandHandler,
  SearchAndReplaceHandler,
  WriteToFileHandler,
  CodebaseSearchHandler,
  ListFilesHandler,
  GrepSearchHandler,
  UseMcpToolHandler,
  AskFollowupQuestionHandler,
  SearchWebHandler,
  FetchWebHandler,
  ParseFigmaHandler,
  SearchMemoryHandler,
  SaveMemoryHandler,
  GetMemoryHandler,
  SearchSpecHandler,
  BrowserActionHandler,
  TestCaseHandler
};

// 便于使用的工具处理器映射
export const TOOL_HANDLERS = {
  update_phase: UpdatePhaseHandler,
  render_research_plan: RenderResearchPlanHandler,
  edit_file: EditFileHandler,
  read_file: ReadFileHandler,
  execute_command: ExecuteCommandHandler,
  replace_in_file: SearchAndReplaceHandler,
  write_to_file: WriteToFileHandler,
  codebase_search: CodebaseSearchHandler,
  list_files: ListFilesHandler,
  grep_search: GrepSearchHandler,
  use_mcp_tool: UseMcpToolHandler,
  ask_followup_question: AskFollowupQuestionHandler,
  command_status_check: CommandStatusCheckHandler,
  browser_action: BrowserActionHandler,
  test_case: TestCaseHandler,
  project_preview: ProjectPreviewHandler,
  parse_figma: ParseFigmaHandler,
  search_web: SearchWebHandler,
  fetch_web: FetchWebHandler,
  search_memory: SearchMemoryHandler,
  save_memory: SaveMemoryHandler,
  get_memory: GetMemoryHandler,
  search_spec: SearchSpecHandler,
  write_todo: TodoWriteHandler,
  read_todo: TodoReadHandler
} as const;

// 工具名称类型
export type ToolName = keyof typeof TOOL_HANDLERS;

// 工具处理器实例类型
export type ToolHandlerInstance = InstanceType<(typeof TOOL_HANDLERS)[ToolName]>;
